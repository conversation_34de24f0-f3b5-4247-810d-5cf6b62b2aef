<!-- Profile Page Container -->
<div class="min-h-screen bg-gradient-to-br from-slate-900 via-blue-950 to-gray-900 relative overflow-hidden">

  <!-- Main Content Container -->
  <div class="relative z-10 flex min-h-screen">
    <!-- Left Sidebar -->
    <div class="w-80 bg-gradient-to-b from-slate-800/95 to-blue-900/95 backdrop-blur-sm border-r border-slate-600/30 flex flex-col">
      <!-- Logo Section -->
      <div class="p-6 border-b border-slate-600/30">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-700 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-lg">T</span>
          </div>
          <div>
            <h1 class="text-white font-bold text-xl">TOY FOR TOI</h1>
            <p class="text-blue-300 text-sm">Игровая платформа</p>
          </div>
        </div>
      </div>

      <!-- Navigation Menu -->
      <div class="flex-1 p-6 space-y-8">
        <!-- My Games Section -->
        <div class="mb-8">
          <h2 class="text-white font-semibold text-lg mb-4">Мои игры</h2>
          <div class="space-y-2">
            <a href="#" class="block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-slate-700/50">
              Библиотека
            </a>
            <a href="#" class="block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-slate-700/50">
              Загрузки
            </a>
          </div>
        </div>

        <!-- Account Section -->
        <div class="mb-8">
          <h2 class="text-white font-semibold text-lg mb-4">Учетная запись</h2>
          <div class="space-y-2">
            <div (click)="setActiveSection('profile')" [ngClass]="{'bg-slate-700/70': activeSection === 'profile'}" class="w-full text-left block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-slate-700/50 cursor-pointer">
              Параметры профиля
            </div>
            <a href="#" class="block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-slate-700/50">
              Пароль и безопасность
            </a>
          </div>
        </div>

        <!-- Store Section -->
        <div class="mb-8">
          <h2 class="text-white font-semibold text-lg mb-4">Магазин</h2>
          <div class="space-y-2">
            <a href="#" class="block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-slate-700/50">
              Каталог игр
            </a>
            <a href="#" class="block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-slate-700/50">
              Корзина
            </a>
            <a href="#" class="block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-slate-700/50">
              История покупок
            </a>
          </div>
        </div>

        <!-- Administration Section (Only for Staff) -->
        <div *ngIf="userProfile?.is_staff" class="mb-8">
          <h2 class="text-white font-semibold text-lg mb-4">Администрирование</h2>
          <div class="space-y-2">
            <div (click)="setActiveSection('users')" [ngClass]="{'bg-slate-700/70': activeSection === 'users'}" class="w-full text-left block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-slate-700/50 cursor-pointer">
              Управление пользователями
            </div>
            <div (click)="setActiveSection('games')" [ngClass]="{'bg-slate-700/70': activeSection === 'games'}" class="w-full text-left block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-slate-700/50 cursor-pointer">
              Управление играми
            </div>
          </div>
        </div>
      </div>

      <!-- Logout Button -->
      <div class="p-6 border-t border-slate-600/30">
        <button (click)="onLogout()" class="w-full bg-gradient-to-r from-red-600 to-red-700 text-white font-medium py-3 px-4 rounded-lg shadow-lg hover:from-red-700 hover:to-red-800 transition-all transform hover:scale-[1.02] profile-button">
          <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
          </svg>
          Выйти
        </button>
      </div>
    </div>

    <!-- Right Content Area -->
    <div class="flex-1 p-8 overflow-y-auto">
      <!-- Loading State -->
      <app-loading-spinner
        *ngIf="isLoading"
        [overlay]="true">
      </app-loading-spinner>

      <!-- Error State -->
      <div *ngIf="errorMessage && !isLoading" class="flex items-center justify-center h-64">
        <div class="text-center">
          <div class="bg-red-500/20 border border-red-500/50 rounded-lg p-6 max-w-md">
            <h3 class="text-red-300 font-semibold mb-2">Ошибка загрузки</h3>
            <p class="text-red-200 mb-4">{{ errorMessage }}</p>
            <button (click)="loadUserProfile()" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
              Попробовать снова
            </button>
          </div>
        </div>
      </div>

      <!-- Profile Content -->
      <div *ngIf="userProfile && !isLoading" class="max-w-6xl">
        <!-- Header with User Icon and Title -->
        <div class="flex items-center mb-8">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-600 to-slate-700 rounded-full flex items-center justify-center mr-6">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div>
            <h1 class="text-4xl font-bold text-white mb-2" *ngIf="activeSection === 'profile'">Параметры</h1>
            <h1 class="text-4xl font-bold text-white mb-2" *ngIf="activeSection === 'users'">Управление пользователями</h1>
            <h1 class="text-4xl font-bold text-white mb-2" *ngIf="activeSection === 'games'">Управление играми</h1>
            <p class="text-gray-300" *ngIf="activeSection === 'profile'">Управляйте данными своей учетной записи</p>
            <p class="text-gray-300" *ngIf="activeSection === 'users'">Просмотр и управление пользователями системы</p>
            <p class="text-gray-300" *ngIf="activeSection === 'games'">Создание, редактирование и удаление игр</p>
          </div>
        </div>

        <!-- Profile Section -->
        <div *ngIf="activeSection === 'profile'">
          <!-- Account Information Section -->
          <div class="bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-8 mb-8 profile-section">
            <h3 class="text-lg font-semibold text-white mb-6">Информация об аккаунте</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Email Field -->
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Email</label>
                <div class="relative">
                  <input type="email" [value]="userProfile.email" readonly class="w-full px-4 py-3 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent profile-input" />
                  <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                </div>
              </div>

              <!-- Password Field -->
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Пароль</label>
                <div class="relative">
                  <input type="password" value="••••••••" readonly class="w-full px-4 py-3 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent profile-input" />
                  <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                  </div>
                </div>
              </div>

              <!-- Phone Field -->
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Телефон</label>
                <div class="relative">
                  <input type="tel" placeholder="+7 (___) ___-__-__" class="w-full px-4 py-3 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent profile-input" />
                  <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-wrap gap-4 mt-8">
              <button class="px-6 py-3 bg-gradient-to-r from-blue-600 to-slate-700 text-white font-medium rounded-lg shadow-lg hover:from-blue-700 hover:to-slate-800 transition-all transform hover:scale-[1.02] profile-button">
                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Обновить профиль
              </button>
              <button (click)="refreshProfile()" class="px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg shadow-lg hover:from-green-700 hover:to-emerald-700 transition-all transform hover:scale-[1.02] profile-button">
                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Обновить данные
              </button>
              <button (click)="verifyToken()" class="px-6 py-3 bg-gradient-to-r from-yellow-600 to-orange-600 text-white font-medium rounded-lg shadow-lg hover:from-yellow-700 hover:to-orange-700 transition-all transform hover:scale-[1.02] profile-button">
                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Проверить токен
              </button>
            </div>
          </div>

          <!-- User Status Info -->
          <div class="bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-6 profile-section">
            <h3 class="text-lg font-semibold text-white mb-4">Статус аккаунта</h3>
            <div class="space-y-3">
              <div class="flex justify-between items-center">
                <span class="text-gray-300">ID пользователя:</span>
                <span class="text-white font-medium">{{ userProfile.id }}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-300">Статус:</span>
                <span [ngClass]="userProfile.is_staff ? 'text-green-400' : 'text-blue-400'" class="font-medium">
                  {{ userProfile.is_staff ? 'Администратор' : 'Пользователь' }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Users Management Section -->
        <div *ngIf="activeSection === 'users'" class="space-y-6">
          <!-- Search and Filters -->
          <div class="bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <!-- Search Input -->
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Поиск</label>
                <input
                  type="text"
                  [(ngModel)]="searchTerm"
                  (ngModelChange)="onSearchChange()"
                  placeholder="Email, имя пользователя..."
                  class="w-full px-3 py-2 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
              </div>

              <!-- Role Filter -->
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Роль</label>
                <select
                  [(ngModel)]="selectedRole"
                  (ngModelChange)="onRoleFilterChange()"
                  class="w-full px-3 py-2 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">Все роли</option>
                  <option value="superuser">Суперпользователи</option>
                  <option value="staff">Администраторы</option>
                  <option value="user">Пользователи</option>
                </select>
              </div>

              <!-- Status Filter -->
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Статус</label>
                <select
                  [(ngModel)]="selectedStatus"
                  (ngModelChange)="onStatusFilterChange()"
                  class="w-full px-3 py-2 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">Все статусы</option>
                  <option value="active">Активные</option>
                  <option value="inactive">Неактивные</option>
                </select>
              </div>

              <!-- Sort By -->
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Сортировка</label>
                <select
                  [(ngModel)]="sortBy"
                  (ngModelChange)="onSortChange()"
                  class="w-full px-3 py-2 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="-date_joined">Дата регистрации (новые)</option>
                  <option value="date_joined">Дата регистрации (старые)</option>
                  <option value="email">Email (А-Я)</option>
                  <option value="-email">Email (Я-А)</option>
                  <option value="username">Имя (А-Я)</option>
                  <option value="-username">Имя (Я-А)</option>
                </select>
              </div>
            </div>

            <!-- Results Summary -->
            <div class="mt-4 flex justify-between items-center text-sm text-gray-400">
              <span>Найдено пользователей: {{ totalUsers }}</span>
              <button
                (click)="loadUsers()"
                class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors"
              >
                Обновить
              </button>
            </div>
          </div>

          <!-- Loading State -->
          <div *ngIf="usersLoading" class="flex justify-center py-8">
            <app-loading-spinner></app-loading-spinner>
          </div>

          <!-- Error State -->
          <div *ngIf="usersError && !usersLoading" class="bg-red-900/20 border border-red-500/50 rounded-lg p-4">
            <p class="text-red-400">{{ usersError }}</p>
            <button (click)="loadUsers()" class="mt-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors">
              Повторить попытку
            </button>
          </div>

          <!-- Users Table -->
          <div *ngIf="!usersLoading && !usersError" class="bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead class="bg-slate-800/60">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Пользователь</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Email</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Роль</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Статус</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Дата регистрации</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Последний вход</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Действия</th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-slate-700/50">
                  <tr *ngFor="let user of users" class="hover:bg-slate-700/20 transition-colors">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{{ user.id }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-600 to-slate-700 rounded-full flex items-center justify-center mr-3">
                          <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                          </svg>
                        </div>
                        <div>
                          <div class="text-sm font-medium text-white">{{ user.username }}</div>
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{{ user.email }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span [ngClass]="getUserRoleClass(user)" class="text-sm font-medium">
                        {{ getUserRoleText(user) }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span [ngClass]="user.is_active ? 'bg-green-900/50 text-green-400 border-green-500/50' : 'bg-red-900/50 text-red-400 border-red-500/50'"
                            class="inline-flex px-2 py-1 text-xs font-semibold rounded-full border">
                        {{ user.is_active ? 'Активен' : 'Неактивен' }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{{ formatDate(user.date_joined) }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      {{ user.last_login ? formatDate(user.last_login) : 'Никогда' }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div class="flex space-x-2">
                        <!-- Toggle Active Status -->
                        <button
                          (click)="toggleUserStatus(user)"
                          [ngClass]="user.is_active ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'"
                          class="px-2 py-1 text-white text-xs rounded transition-colors"
                          [title]="user.is_active ? 'Деактивировать' : 'Активировать'"
                        >
                          {{ user.is_active ? 'Деактивировать' : 'Активировать' }}
                        </button>

                        <!-- Toggle Staff Status (only for superusers) -->
                        <button
                          *ngIf="userProfile?.is_superuser && !user.is_superuser"
                          (click)="toggleStaffStatus(user)"
                          [ngClass]="user.is_staff ? 'bg-orange-600 hover:bg-orange-700' : 'bg-blue-600 hover:bg-blue-700'"
                          class="px-2 py-1 text-white text-xs rounded transition-colors"
                          [title]="user.is_staff ? 'Убрать права админа' : 'Дать права админа'"
                        >
                          {{ user.is_staff ? 'Убрать админа' : 'Сделать админом' }}
                        </button>

                        <!-- Delete User (only for superusers, can't delete self) -->
                        <button
                          *ngIf="userProfile?.is_superuser && user.id !== userProfile.id"
                          (click)="deleteUser(user)"
                          class="px-2 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors"
                          title="Удалить пользователя"
                        >
                          Удалить
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Pagination -->
            <div *ngIf="totalPages > 1" class="bg-gray-800/30 px-6 py-3 border-t border-gray-700/50">
              <div class="flex items-center justify-between">
                <div class="text-sm text-gray-400">
                  Страница {{ currentPage }} из {{ totalPages }} ({{ totalUsers }} пользователей)
                </div>
                <div class="flex space-x-1">
                  <!-- Previous Page -->
                  <button
                    (click)="onPageChange(currentPage - 1)"
                    [disabled]="currentPage === 1"
                    [ngClass]="currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-slate-700'"
                    class="px-3 py-1 bg-slate-800 text-white text-sm rounded transition-colors"
                  >
                    ←
                  </button>

                  <!-- Page Numbers -->
                  <button
                    *ngFor="let page of pages"
                    (click)="onPageChange(page)"
                    [ngClass]="page === currentPage ? 'bg-blue-600' : 'bg-slate-800 hover:bg-slate-700'"
                    class="px-3 py-1 text-white text-sm rounded transition-colors"
                  >
                    {{ page }}
                  </button>

                  <!-- Next Page -->
                  <button
                    (click)="onPageChange(currentPage + 1)"
                    [disabled]="currentPage === totalPages"
                    [ngClass]="currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-slate-700'"
                    class="px-3 py-1 bg-slate-800 text-white text-sm rounded transition-colors"
                  >
                    →
                  </button>
                </div>
              </div>
            </div>

            <!-- Empty State -->
            <div *ngIf="users.length === 0 && !usersLoading" class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-300">Пользователи не найдены</h3>
              <p class="mt-1 text-sm text-gray-400">Попробуйте изменить параметры поиска или фильтры.</p>
            </div>
          </div>
        </div>

        <!-- Games Management Section -->
        <div *ngIf="activeSection === 'games'">
          <div class="bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-8 mb-8 profile-section">
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-lg font-semibold text-white">Управление играми</h3>
              <button class="px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg shadow-lg hover:from-green-700 hover:to-emerald-700 transition-all transform hover:scale-[1.02]">
                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Добавить игру
              </button>
            </div>

            <!-- Loading State -->
            <app-loading-spinner
              *ngIf="gamesLoading"
              size="medium">
            </app-loading-spinner>

            <!-- Error State -->
            <div *ngIf="gamesError && !gamesLoading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-6">
              <h4 class="text-red-300 font-semibold mb-2">Ошибка загрузки игр</h4>
              <p class="text-red-200 mb-4">{{ gamesError }}</p>
              <button (click)="loadGames()" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                Попробовать снова
              </button>
            </div>

            <!-- Games List -->
            <div *ngIf="!gamesLoading && !gamesError" class="space-y-4">
              <div *ngFor="let game of games" class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-6">
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <h4 class="text-white font-semibold text-lg mb-2">{{ game.title }}</h4>
                    <p class="text-gray-300 mb-3">{{ game.description }}</p>
                    <div class="flex flex-wrap gap-4 text-sm text-gray-400">
                      <span>Цена: <span class="text-green-400 font-medium">${{ game.price }}</span></span>
                      <span>Пробная версия: <span [ngClass]="game.trial_available ? 'text-green-400' : 'text-red-400'">{{ game.trial_available ? 'Доступна' : 'Недоступна' }}</span></span>
                      <span>Создано: <span class="text-blue-400">{{ game.created_at | date:'short' }}</span></span>
                    </div>
                  </div>
                  <div class="flex space-x-2 ml-4">
                    <button class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors">
                      Редактировать
                    </button>
                    <button (click)="deleteGame(game.id)" class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors">
                      Удалить
                    </button>
                  </div>
                </div>
              </div>

              <div *ngIf="games.length === 0" class="text-center py-8 text-gray-400">
                Игры не найдены
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
