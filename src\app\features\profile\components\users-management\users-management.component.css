/* Users Management specific styles */
.users-table {
  transition: all 0.3s ease;
}

.user-row:hover {
  background-color: rgba(51, 65, 85, 0.2);
}

.action-button {
  transition: all 0.2s ease;
}

.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.pagination-button {
  transition: all 0.2s ease;
}

.pagination-button:hover:not(:disabled) {
  transform: translateY(-1px);
}

.filter-input {
  transition: all 0.3s ease;
}

.filter-input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
