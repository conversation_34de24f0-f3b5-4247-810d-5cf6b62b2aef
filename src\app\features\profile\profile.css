/* Profile-specific styles */
.animated-gradient {
  background: linear-gradient(45deg, #1e293b, #334155, #0f172a, #1e293b);
  background-size: 400% 400%;
  animation: gradientShift 12s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.network-animation {
  animation: networkPulse 4s ease-in-out infinite;
}

@keyframes networkPulse {
  0%, 100% { opacity: 0.2; }
  50% { opacity: 0.4; }
}

/* Sidebar responsive styles */
@media (max-width: 1024px) {
  .profile-sidebar {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .profile-layout {
    flex-direction: column;
  }

  .profile-sidebar {
    width: 100%;
    min-height: auto;
    max-height: 300px;
    overflow-y: auto;
  }

  .profile-content {
    padding: 1rem;
  }

  .profile-grid {
    grid-template-columns: 1fr;
  }
}

/* Custom scrollbar for sidebar */
.profile-sidebar::-webkit-scrollbar {
  width: 6px;
}

.profile-sidebar::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.1);
}

.profile-sidebar::-webkit-scrollbar-thumb {
  background: rgba(51, 65, 85, 0.3);
  border-radius: 3px;
}

.profile-sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(51, 65, 85, 0.5);
}

/* Enhanced input field styles */
.profile-input {
  transition: all 0.3s ease;
}

.profile-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* Button hover effects */
.profile-button {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.profile-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.profile-button:hover::before {
  left: 100%;
}

/* Smooth section transitions */
.profile-section {
  transition: all 0.3s ease;
}

.profile-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}
